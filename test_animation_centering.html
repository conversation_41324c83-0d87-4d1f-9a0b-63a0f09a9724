<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Animation Centering</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @font-face {
            font-family: "GravityVF";
            src: url("src/wp-content/themes/ps-2023/resources/fonts/ABCGravityUprightVariable.ttf") format("truetype-variations");
            font-weight: 100 900;
            font-stretch: 50% 200%;
        }

        /* Test styles - copied from the modified header-tresk.blade.php */
        .word {
            position: absolute;
            left: 50%;
            top: 0;
            width: max-content; /* allow word to use its natural width */
            white-space: nowrap; /* prevent line breaks */
            transform: translateX(-50%); /* center the entire word */
            transform-origin: left center; /* expand from left to right */
        }

        .word span {
            font-family: "GravityVF", Arial, sans-serif;
            color: #fff; /* white on navy */
            display: inline-block;
            font-variation-settings: "wdth" var(--wd); /* start expanded */
            line-height: 95%;
            font-size: clamp(
                4.3rem,
                calc(4.3rem + (262.4 - 130) * ((100vw - 375px) / (1280 - 375))),
                16.4rem
            );
            transform-origin: left center; /* expand from left to right */
        }

        /* R letter gets tighter spacing with E */
        .word span:nth-child(2) {
            margin-right: -0.025em;
        }

        /* E letter gets tighter spacing with S (20% smaller gap) */
        .word span:nth-child(3) {
            margin-right: -0.05em;
        }

        /* K letter gets extra 15% horizontal scaling */
        .word span:nth-child(5) {
            transform: scaleX(1.15);
            transform-origin: left center; /* expand from left to right */
        }

        /* K letter animation preserves its 15% extra scaling */
        .word-container:hover .word span:nth-child(5) {
            animation: stretch-k 1s cubic-bezier(.75, 1.45, .14, 1) infinite alternate;
        }

        /* K letter gets looser spacing with ! (20% larger gap) */
        .word span:nth-child(5) {
            margin-right: 0.05em;
        }

        /* exclamation mark displays like other letters */
        .bang {
            opacity: 1; /* visible like other letters */
        }

        /* hover animation: condense and expand continuously */
        .word-container:hover .word span {
            animation: stretch 1s cubic-bezier(.75, 1.45, .14, 1) infinite alternate;
        }

        /* width keyframes: condense then expand from left to right */
        @keyframes stretch {
            from {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.6);
            }
            /* condense first, then expand from left */
            to {
                font-variation-settings: "wdth" var(--wd);
                transform: scaleX(1);
            }
        }

        /* K letter keyframes: preserve 15% extra scaling */
        @keyframes stretch-k {
            from {
                font-variation-settings: "wdth" 60;
                transform: scaleX(0.69); /* 0.6 * 1.15 */
            }
            /* condense first, then expand from left with 15% extra scaling */
            to {
                font-variation-settings: "wdth" var(--wd);
                transform: scaleX(1.15); /* 1 * 1.15 */
            }
        }

        /* Background gradient for testing */
        .bg-ps-gradient-blue-2 {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        }

        /* Center line indicator for testing */
        .test-center-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: red;
            z-index: 10;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <section class="bg-ps-gradient-blue-2 h-screen pt-20 lg:pt-0 relative">
        <!-- Center line indicator for testing -->
        <div class="test-center-line"></div>
        
        <div class="container mx-auto flex h-full flex-col justify-center items-center px-4 py-6 lg:pb-6 lg:pt-[8.5rem] mb-32 sm:mb-0">
            <div class="flex gap-x-4 flex-col sm:flex-row justify-center items-center w-full">
                <div class="flex flex-col gap-4 sm:gap-0 justify-center flex-1 w-full text-center">
                    
                    <!-- Title Section -->
                    <header class="flex flex-col gap-y-4 xl:gap-y-12 tresk-title text-blue-200 text-5xl xl:text-[80px] font-bold uppercase leading-[50%] w-full">
                        <div>Test Title</div>
                        <div class="word-container relative w-full h-[clamp(68.8px,calc(68.8px+(262.4-130)*((100vw-375px)/(1280-375))),262.4px)]">
                            <div class="word uppercase">
                                <span style="--wd:61">T</span>
                                <span style="--wd:69">R</span>
                                <span style="--wd:100">E</span>
                                <span style="--wd:121">S</span>
                                <span style="--wd:150">K</span>
                                <span class="bang" style="--wd:150">!</span>
                            </div>
                        </div>
                    </header>
                    
                    <div class="text-white text-lg mt-8">
                        <p>Hover over "TRESK!" to see the animation</p>
                        <p>The red line shows the center - the word should be centered on it</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
